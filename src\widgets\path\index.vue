<!-- 模板 -->
<template>
  <div class="mainContainer">
    <div id="cesium-viewer" ref="viewerContainer"></div>
  </div>
</template>

<script setup lang="ts">
import * as Cesium from "cesium";

import { onBeforeUnmount, onMounted, ref } from "vue";

import { initMap } from "@/utils/initMap";

const viewerContainer = ref<HTMLElement | null>(null);
let viewer: Cesium.Viewer | null = null;

const initViewer = async () => {
  viewer = await initMap(viewerContainer);
};

onMounted(() => {
  initViewer();
});

onBeforeUnmount(() => {
  if (viewer) {
    viewer.destroy();
  }
});
</script>
